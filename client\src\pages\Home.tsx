import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Search, Users, Shield, Clock } from 'lucide-react';

const Home = () => {
  console.log('Home component is rendering');
  return (
    <div className="min-h-screen bg-white">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/" className="text-2xl font-bold text-blue-600">
                FreelanceHub
              </Link>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link to="/dashboard" className="text-gray-700 hover:text-blue-600 transition-colors">
                Browse Gigs
              </Link>
              <a href="#features" className="text-gray-700 hover:text-blue-600 transition-colors">
                How it Works
              </a>
              <Link to="/create-gig" className="text-gray-700 hover:text-blue-600 transition-colors">
                Become a Seller
              </Link>
            </nav>
            <div className="flex items-center space-x-4">
              <Link to="/login" className="text-gray-700 hover:text-blue-600">
                Sign In
              </Link>
              <Link to="/register" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                Join Now
              </Link>
            </div>
          </div>
        </div>
      </header>

      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800 text-white py-24 overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-96 h-96 bg-yellow-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-fade-in-up">
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                Find the perfect
                <span className="block bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent">
                  freelance services
                </span>
                for your business
              </h1>
            </div>

            <div className="animate-fade-in-up delay-200">
              <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
                Join millions of entrepreneurs and businesses who trust FreelanceHub to turn their ideas into reality.
              </p>
            </div>

            {/* Search Bar */}
            <div className="max-w-3xl mx-auto animate-fade-in-up delay-400">
              <div className="flex bg-white rounded-2xl p-3 shadow-2xl backdrop-blur-sm">
                <input
                  type="text"
                  placeholder="Try 'building mobile app', 'logo design', 'content writing'..."
                  className="flex-1 px-6 py-4 text-gray-700 focus:outline-none text-lg rounded-l-xl"
                />
                <button
                  type="button"
                  className="bg-blue-600 text-white px-8 py-4 rounded-xl hover:bg-blue-700 flex items-center transition-all duration-200 transform hover:scale-105 shadow-lg"
                  aria-label="Search for services"
                >
                  <Search className="w-6 h-6 mr-2" />
                  <span className="font-semibold">Search</span>
                </button>
              </div>
            </div>

            {/* Popular Searches */}
            <div className="mt-8 animate-fade-in-up delay-600">
              <p className="text-blue-200 mb-4">Popular searches:</p>
              <div className="flex flex-wrap justify-center gap-3">
                {['Web Development', 'Logo Design', 'Content Writing', 'Mobile Apps', 'SEO'].map((tag) => (
                  <span
                    key={tag}
                    className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm hover:bg-white/30 transition-all cursor-pointer transform hover:scale-105"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="features" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              A whole world of freelance talent at your fingertips
            </h2>
            <p className="text-xl text-gray-600">
              The best part? Everything happens on one platform.
            </p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-gradient-to-br from-blue-100 to-blue-200 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-lg transition-all duration-300">
                <Search className="w-10 h-10 text-blue-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">Browse & Discover</h3>
              <p className="text-gray-600 leading-relaxed">Find the right freelancer to begin working on your project within minutes using our smart matching system.</p>
            </div>

            <div className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-gradient-to-br from-green-100 to-emerald-200 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-lg transition-all duration-300">
                <Users className="w-10 h-10 text-green-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">Collaborate</h3>
              <p className="text-gray-600 leading-relaxed">Use our built-in messaging system, file sharing, and project management tools to work seamlessly.</p>
            </div>

            <div className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-gradient-to-br from-purple-100 to-violet-200 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-lg transition-all duration-300">
                <Shield className="w-10 h-10 text-purple-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">Payment Protection</h3>
              <p className="text-gray-600 leading-relaxed">Only pay when you're 100% satisfied with the delivered work. Your money is always protected.</p>
            </div>

            <div className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-gradient-to-br from-orange-100 to-yellow-200 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-lg transition-all duration-300">
                <Clock className="w-10 h-10 text-orange-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">24/7 Support</h3>
              <p className="text-gray-600 leading-relaxed">Questions? Our round-the-clock support team is available to help anytime, anywhere.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8 text-center text-white">
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">2M+</div>
              <div className="text-blue-100">Active Freelancers</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">500K+</div>
              <div className="text-blue-100">Happy Clients</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">1M+</div>
              <div className="text-blue-100">Projects Completed</div>
            </div>
            <div className="group">
              <div className="text-3xl md:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">99%</div>
              <div className="text-blue-100">Satisfaction Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Explore Popular Categories
            </h2>
            <p className="text-xl text-gray-600">
              Find the perfect service for your business needs
            </p>
          </div>

          <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[
              { name: 'Web Development', icon: '💻', color: 'from-blue-500 to-cyan-500' },
              { name: 'Graphic Design', icon: '🎨', color: 'from-purple-500 to-pink-500' },
              { name: 'Digital Marketing', icon: '📈', color: 'from-green-500 to-emerald-500' },
              { name: 'Writing & Translation', icon: '✍️', color: 'from-orange-500 to-red-500' },
              { name: 'Video & Animation', icon: '🎬', color: 'from-indigo-500 to-purple-500' },
              { name: 'Music & Audio', icon: '🎵', color: 'from-pink-500 to-rose-500' },
              { name: 'Programming', icon: '⚡', color: 'from-yellow-500 to-orange-500' },
              { name: 'Business', icon: '💼', color: 'from-gray-500 to-slate-500' },
            ].map((category) => (
              <div
                key={category.name}
                className="group cursor-pointer"
              >
                <div className={`bg-gradient-to-br ${category.color} p-6 rounded-2xl text-white transform group-hover:scale-105 transition-all duration-300 shadow-lg group-hover:shadow-xl`}>
                  <div className="text-4xl mb-3">{category.icon}</div>
                  <h3 className="font-semibold text-lg">{category.name}</h3>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to get started?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Join millions of people who use FreelanceHub to turn their ideas into reality.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/register"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              Get Started Today
            </Link>
            <Link
              to="/dashboard"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-200 transform hover:scale-105"
            >
              Browse Gigs
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
