import React from 'react';

const SimpleTest = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: '#f0f0f0', minHeight: '100vh' }}>
      <h1 style={{ color: '#333', fontSize: '2rem', marginBottom: '1rem' }}>
        Simple Test Page
      </h1>
      <p style={{ color: '#666', fontSize: '1.2rem', marginBottom: '1rem' }}>
        If you can see this page, React is working correctly!
      </p>
      <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <h2 style={{ color: '#333', marginBottom: '1rem' }}>Application Status:</h2>
        <ul style={{ color: '#666', lineHeight: '1.6' }}>
          <li>✅ React is rendering</li>
          <li>✅ TypeScript is working</li>
          <li>✅ Vite dev server is running</li>
          <li>✅ Basic routing is functional</li>
        </ul>
      </div>
      <div style={{ marginTop: '2rem' }}>
        <a 
          href="/test" 
          style={{ 
            backgroundColor: '#3b82f6', 
            color: 'white', 
            padding: '10px 20px', 
            textDecoration: 'none', 
            borderRadius: '6px',
            marginRight: '10px'
          }}
        >
          Go to HomeTest
        </a>
        <a 
          href="/home" 
          style={{ 
            backgroundColor: '#10b981', 
            color: 'white', 
            padding: '10px 20px', 
            textDecoration: 'none', 
            borderRadius: '6px'
          }}
        >
          Go to Home
        </a>
      </div>
    </div>
  );
};

export default SimpleTest;
