import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginCredentials, RegisterData } from '../types';
import { authAPI } from '../utils/api';
import toast from 'react-hot-toast';

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  checkAuth: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<boolean>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false,
      isAuthenticated: false,

      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true });
        try {
          const response = await authAPI.login(credentials);
          
          if (response.success && response.token && response.user) {
            const { token, user } = response;
            
            // Store in localStorage
            localStorage.setItem('token', token);
            localStorage.setItem('user', JSON.stringify(user));
            
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false,
            });
            
            toast.success('Login successful!');
            return true;
          } else {
            toast.error(response.message || 'Login failed');
            set({ isLoading: false });
            return false;
          }
        } catch (error: any) {
          const message = error.response?.data?.message || 'Login failed';
          toast.error(message);
          set({ isLoading: false });
          return false;
        }
      },

      register: async (data: RegisterData) => {
        set({ isLoading: true });
        try {
          const response = await authAPI.register(data);
          
          if (response.success && response.token && response.user) {
            const { token, user } = response;
            
            // Store in localStorage
            localStorage.setItem('token', token);
            localStorage.setItem('user', JSON.stringify(user));
            
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false,
            });
            
            toast.success('Registration successful!');
            return true;
          } else {
            toast.error(response.message || 'Registration failed');
            set({ isLoading: false });
            return false;
          }
        } catch (error: any) {
          const message = error.response?.data?.message || 'Registration failed';
          toast.error(message);
          set({ isLoading: false });
          return false;
        }
      },

      logout: () => {
        // Clear localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
        
        toast.success('Logged out successfully');
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userData };
          localStorage.setItem('user', JSON.stringify(updatedUser));
          set({ user: updatedUser });
        }
      },

      checkAuth: async () => {
        const token = localStorage.getItem('token');
        const userStr = localStorage.getItem('user');

        if (token && userStr) {
          try {
            const user = JSON.parse(userStr);

            // For development, just use stored user data without API call
            // This prevents the app from hanging when the server is not running
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false,
            });

            // Optionally verify token in background (don't await)
            authAPI.getMe().then((response) => {
              if (response.success && response.user) {
                set({
                  user: response.user,
                  token,
                  isAuthenticated: true,
                  isLoading: false,
                });
              } else {
                // Token invalid, clear auth
                get().logout();
              }
            }).catch(() => {
              // Server not available or token invalid
              // For development, keep the user logged in with stored data
              console.log('Server not available, using stored auth data');
            });

          } catch (error) {
            // Invalid stored data, clear auth
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            set({ isLoading: false });
          }
        } else {
          set({ isLoading: false });
        }
      },

      updateProfile: async (data: Partial<User>) => {
        set({ isLoading: true });
        try {
          const response = await authAPI.updateProfile(data);
          
          if (response.success && response.user) {
            const updatedUser = response.user;
            localStorage.setItem('user', JSON.stringify(updatedUser));
            
            set({
              user: updatedUser,
              isLoading: false,
            });
            
            toast.success('Profile updated successfully!');
            return true;
          } else {
            toast.error('Failed to update profile');
            set({ isLoading: false });
            return false;
          }
        } catch (error: any) {
          const message = error.response?.data?.message || 'Failed to update profile';
          toast.error(message);
          set({ isLoading: false });
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
